package br.com.alice.api.ops.controllers.staff.converters

import br.com.alice.api.ops.controllers.staff.models.UserRequest
import br.com.alice.common.Converter
import br.com.alice.common.core.StaffType
import br.com.alice.common.map
import br.com.alice.common.models.CouncilType
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import java.util.UUID

object HealthProfessionalConverter : Converter<UserRequest, HealthProfessional>(
    UserRequest::class, HealthProfessional::class
) {
    fun convert(source: UserRequest, healthProfessionalId: UUID, staffId: UUID): HealthProfessional = convert(
        source,
        map(HealthProfessional::id) from healthProfessionalId,
        map(HealthProfessional::name) from "${source.firstName} ${source.lastName}",
        map(HealthProfessional::status) from if (source.active) SpecialistStatus.ACTIVE else SpecialistStatus.INACTIVE,
        map(HealthProfessional::staffId) from staffId,
        map(HealthProfessional::specialtyId) from source.specialty,
        map(HealthProfessional::subSpecialtyIds) from source.subSpecialties.orEmpty(),
        map(HealthProfessional::imageUrl) from source.profileImageUrl,
        map(HealthProfessional::internalSpecialtyId) from source.internalSpecialty,
        map(HealthProfessional::internalSubSpecialtyIds) from source.internalSubSpecialties.orEmpty(),
        map(HealthProfessional::providerUnitIds) from source.providerUnits,
        map(HealthProfessional::showOnApp) from (source.showOnApp ?: false),
        map(HealthProfessional::onVacationStart) from source.onVacationStart?.atTime(12, 0),
        map(HealthProfessional::onVacationUntil) from source.onVacationUntil?.atTime(12, 0),
        map(HealthProfessional::contacts) from source.contacts?.map {
            ContactConverter.convert(
                it,
                healthProfessionalId,
                if (source.type == StaffType.COMMUNITY_SPECIALIST) StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST else StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
            )
        },
        map(HealthProfessional::council) from source.council?.let { council ->
            Council(
                number = council.number,
                state = council.state,
                type = council.type?.let { CouncilType.fromCode(it) }
            )
        }
    )
}
