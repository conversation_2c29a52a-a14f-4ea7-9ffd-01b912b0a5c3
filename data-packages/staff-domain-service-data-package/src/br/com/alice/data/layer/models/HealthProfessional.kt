package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class HealthProfessional(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val staffId: UUID,
    val profileBio: String? = null,
    val council: Council,
    val specialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID> = emptyList(),
    val internalSpecialtyId: UUID? = null,
    val internalSubSpecialtyIds: List<UUID> = emptyList(),
    val quote: String? = null,
    val urlSlug: String? = null,
    val qualifications: List<Qualification> = emptyList(),
    val imageUrl: String? = null,
    val education: List<String> = emptyList(),
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val providerUnitIds: List<UUID> = emptyList(),
    val showOnApp: Boolean = true,
    val onCall: Boolean = false,
    val staff: Staff? = null,
    val status: SpecialistStatus = SpecialistStatus.ACTIVE,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val paymentFrequency: Int? = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val email: String,
    val name: String,
    val gender: Gender? = null,
    val nationalId: String? = null,
    val type: StaffType,
    val role: Role? = null,
    val searchTokens: String? = null,
    val contactIds: List<UUID> = emptyList(),
    val bankAccountInfo: List<SpecialistBankAccountInfo> = emptyList(),
    val curiosity: String? = null,
    val deAccreditationDate: LocalDate? = null,
    val onVacationUntil: LocalDateTime? = null,
    val onVacationStart: LocalDateTime? = null,
    @Deprecated("Use contacts instead")
    val phones: List<PhoneNumber> = emptyList(),
    @Deprecated("Use contacts instead")
    val scheduleAvailabilityDays: Int? = null,
    @Deprecated("Use contacts instead")
    val appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
    /** computed field */
    val contacts: List<Contact>? = null,
    @Deprecated("Use contacts instead")
    val addressesStructured: List<StructuredAddress>? = null,
) {

    fun sanitize(): HealthProfessional {
        return copy(
            council = this.council.copy(number = this.council.number.trim()),
            imageUrl = if (imageUrl.isNullOrBlank()) null else imageUrl,
            nationalId = this.nationalId?.onlyDigits()?.trim().nullIfBlank(),
        )
    }

    val councilName
        get() = when {
            staff == null -> ""
            staff.isPhysician() -> HealthProfessionalCouncil.CRM.toString()
            staff.isNurse() -> HealthProfessionalCouncil.COREN.toString()
            staff.isNutritionist() -> HealthProfessionalCouncil.CRN.toString()
            staff.isPhysicalEducator() -> HealthProfessionalCouncil.CREF.toString()
            staff.isPsychologist() -> HealthProfessionalCouncil.CRP.toString()
            else -> ""
        }

    val councilSignature
        get() = if (councilName.isNotBlank()) "$councilName: $council" else council.toString()

    suspend fun validate() = catchResult<HealthProfessional, Throwable> {
        if (type == StaffType.COMMUNITY_SPECIALIST) {
            validateHealthSpecialistScore()
            validateTiers()
        }

        return@catchResult this.success()
    }

    fun isOnVacation(): Boolean =
        takeIf { this.onVacationStart != null && this.onVacationUntil != null }
            ?.let {
                val now = LocalDateTime.now().toSaoPauloTimeZone().toLocalDate()
                val starts = onVacationStart!!.toSaoPauloTimeZone().toLocalDate()
                val until = onVacationUntil!!.toSaoPauloTimeZone().toLocalDate()
                now in starts..until
            } ?: false

    fun hasVacationOverlapWith(from: LocalDate, to: LocalDate): Boolean =
        takeIf { this.onVacationStart != null && this.onVacationUntil != null }
            ?.let {
                val starts = onVacationStart!!.toSaoPauloTimeZone().toLocalDate()
                val until = onVacationUntil!!.toSaoPauloTimeZone().toLocalDate()
                val startPeriodIsAfterEndVacation = from.isAfter(until)
                val endPeriodIsBeforeStartVacation = to.isBefore(starts)
                val hasOverlap = !(startPeriodIsAfterEndVacation || endPeriodIsBeforeStartVacation)
                hasOverlap
            } ?: false

    private fun validateHealthSpecialistScore() {
        if (healthSpecialistScore == null) {
            throw InvalidArgumentException("Especialista da Comunidade deve ter o campo Vivendo o Impossível definido")
        }
    }

    private fun validateTiers() {
        if (theoristTier == null) {
            throw InvalidArgumentException("Especialista da Comunidade deve ter o campo Tier Teórico definido")
        }
        if (theoristTier == SpecialistTier.OPT_IN) {
            throw InvalidArgumentException("Tier Teórico não pode ser Opt-in")
        }
        if (tierIsGreaterThanTheoristTier) {
            throw InvalidArgumentException("Tier de Atendimento não pode ser maior que o Tier Teórico")
        }
    }

    private val tierRestrictions = mapOf(
        SpecialistTier.TALENTED to listOf(
            SpecialistTier.EXPERT,
            SpecialistTier.SUPER_EXPERT,
            SpecialistTier.ULTRA_EXPERT
        ),
        SpecialistTier.EXPERT to listOf(SpecialistTier.SUPER_EXPERT, SpecialistTier.ULTRA_EXPERT),
        SpecialistTier.SUPER_EXPERT to listOf(SpecialistTier.ULTRA_EXPERT)
    )

    private val tierIsGreaterThanTheoristTier: Boolean
        get() = tierRestrictions[theoristTier]?.contains(tier) == true

}

fun HealthProfessional.withStaff(staff: Staff?): HealthProfessional =
    copy(staff = staff)

fun HealthProfessional.withContacts(contacts: List<Contact>?): HealthProfessional =
    copy(contacts = contacts)
