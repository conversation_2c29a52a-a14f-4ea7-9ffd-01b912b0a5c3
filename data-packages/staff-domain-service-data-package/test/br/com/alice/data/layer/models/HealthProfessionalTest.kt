package br.com.alice.data.layer.models

import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthProfessionalTest: MockedTestHelper() {

    @Test
    fun `#sanitize values`() {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            imageUrl = "  ",
            council = Council(" 123 ", State.SP)
        )

        val sanitizedHealthProfessional = healthProfessional.sanitize()

        assertThat(sanitizedHealthProfessional.imageUrl).isNull()
        assertThat(sanitizedHealthProfessional.council.number).isEqualTo("123")
    }

    @Test
    fun `#validate return success when COMMUNITY_SPECIALIST when is valid`() = runBlocking<Unit> {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.EXPERT
        )
        val result = healthProfessional.validate()
        assertThat(result).isSuccess()

        val healthProfessionalOptIn = TestModelFactory.buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.EXPERT,
            tier = SpecialistTier.TALENTED
        )
        val resultOptIn = healthProfessionalOptIn.validate()
        assertThat(resultOptIn).isSuccess()
    }

    @Test
    fun `#validate should return error when COMMUNITY_SPECIALIST does not have healthSpecialistScore`() =
        runBlocking<Unit> {
            val healthProfessional = TestModelFactory.buildHealthProfessional(
                type = StaffType.COMMUNITY_SPECIALIST,
                healthSpecialistScore = null,
                theoristTier = SpecialistTier.EXPERT
            )

            val result = healthProfessional.validate()
            ResultAssert(result)
                .fails()
                .withMessage("Especialista da Comunidade deve ter o campo Vivendo o Impossível definido")
                .ofType(InvalidArgumentException::class)
        }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST does not have theoristTier`() = runBlocking<Unit> {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = null
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade deve ter o campo Tier Teórico definido")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST theoristTier is OPT_IN`() = runBlocking<Unit> {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.OPT_IN
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Tier Teórico não pode ser Opt-in")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST has tier greater than theoristTier`() =
        runBlocking<Unit> {
            val healthProfessional = TestModelFactory.buildHealthProfessional(
                type = StaffType.COMMUNITY_SPECIALIST,
                healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
                theoristTier = SpecialistTier.EXPERT,
                tier = SpecialistTier.SUPER_EXPERT
            )

            val result = healthProfessional.validate()
            ResultAssert(result)
                .fails()
                .withMessage("Tier de Atendimento não pode ser maior que o Tier Teórico")
                .ofType(InvalidArgumentException::class)
        }

    @Test
    fun `#isOnVacation return true if health professional is on vacation`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = TestModelFactory.buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.minusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.isOnVacation()
                assertThat(result).isTrue()
            }
        }

    @Test
    fun `#isOnVacation return false if health professional is on vacation`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = TestModelFactory.buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.plusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.isOnVacation()
                assertThat(result).isFalse()
            }
        }

    @Test
    fun `#isOnVacation return false if health professional don't have any vacation date`() = runBlocking<Unit> {
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            type = StaffType.HEALTH_PROFESSIONAL,
        )

        val result = healthProfessional.isOnVacation()
        assertThat(result).isFalse()
    }

    @Test
    fun `#hasVacationIntersectionWith return false if the health professional does not have a vacation period that overlaps with the given time frame`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = TestModelFactory.buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.plusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.hasVacationOverlapWith(
                    date.toLocalDate().plusDays(1),
                    date.toLocalDate().plusDays(4)
                )
                assertThat(result).isFalse()
            }
        }

    @Test
    fun `#hasVacationIntersectionWith return true if the health professional has a vacation period that overlaps with the given time frame`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = TestModelFactory.buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.plusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.hasVacationOverlapWith(
                    date.toLocalDate().plusDays(1),
                    date.toLocalDate().plusDays(6)
                )
                val anotherResult = healthProfessional.hasVacationOverlapWith(
                    date.toLocalDate().plusDays(6),
                    date.toLocalDate().plusDays(15)
                )
                assertThat(result).isTrue()
                assertThat(anotherResult).isTrue()
            }
        }

}
