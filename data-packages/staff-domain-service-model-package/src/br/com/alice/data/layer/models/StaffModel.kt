package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.User
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.isValidCpf
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.models.Gender
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.gson.annotations.Expose
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class StaffModel(
    @Expose
    override val email: String,
    val firstName: String,
    val lastName: String,
    val active: Boolean = true,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val nationalId: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    val searchTokens: String? = null,
    @Expose
    override val role: Role,
    @Expose
    val type: StaffType,
    val birthdate: LocalDate? = null,
    val onCall: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : User, Model, AuthorizableModel, UpdatedByReference {
    val fullName = "$firstName $lastName"

    val treatment
        get() = if (gender == Gender.FEMALE) "Dra." else "Dr."

    // These objects could be null although the property being non nullable because they were deserialized
    override fun sanitize(): StaffModel =
        copy(
            email = this.email.lowercase().trim(),
            firstName = this.firstName.trim(),
            lastName = this.lastName.trim(),
            nationalId = this.nationalId?.onlyDigits()?.trim().nullIfBlank(),
            profileImageUrl = if (profileImageUrl.isNullOrBlank()) null else profileImageUrl
        )

    suspend fun validate() = catchResult<StaffModel, Throwable> {
        require(this.role.types.contains(this.type)) { "<${this.type}> can not be a <${this.role}> role" }

        this.nationalId?.let { nationalId ->
            if (nationalId.isValidCpf().not()) {
                return@catchResult InvalidArgumentException(message = "CPF '$nationalId' é inválido").failure()
            }
        }

        this.success()
    }
}
