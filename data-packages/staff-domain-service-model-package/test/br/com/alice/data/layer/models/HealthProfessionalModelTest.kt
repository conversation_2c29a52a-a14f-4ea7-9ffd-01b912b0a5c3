package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class HealthProfessionalModelTest {

    data class TestCase(
        val council: CouncilModel,
        val expectedMessage: String
    )

    companion object {
        @JvmStatic
        fun provideInvalidCouncil() = listOf(
            TestCase(
                council = CouncilModel("", State.SP, CouncilType.CRM),
                expectedMessage = "O número do conselho deve ser preenchido"
            ),
            TestCase(
                council = CouncilModel("123", State.SP, null),
                expectedMessage = "O tipo do conselho deve ser preenchido"
            )
        )
    }

    @Test
    fun `#sanitize values`() {
        val healthProfessional = buildHealthProfessional(
            imageUrl = "  ",
            council = CouncilModel(" 123 ", State.SP)
        )

        val sanitizedHealthProfessional = healthProfessional.sanitize()

        assertThat(sanitizedHealthProfessional.imageUrl).isNull()
        assertThat(sanitizedHealthProfessional.council.number).isEqualTo("123")
    }

    @Test
    fun `#validate return success when COMMUNITY_SPECIALIST when is valid`() = runBlocking<Unit> {
        val healthProfessional = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.EXPERT
        )
        val result = healthProfessional.validate()
        ResultAssert.assertThat(result).isSuccess()

        val healthProfessionalOptIn = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.EXPERT,
            tier = SpecialistTier.TALENTED
        )
        val resultOptIn = healthProfessionalOptIn.validate()
        ResultAssert.assertThat(resultOptIn).isSuccess()
    }

    @Test
    fun `#validate should return error when COMMUNITY_SPECIALIST does not have healthSpecialistScore`() = runBlocking<Unit> {
        val healthProfessional = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = null,
            theoristTier = SpecialistTier.EXPERT
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade deve ter o campo Vivendo o Impossível definido")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST does not have theoristTier`() = runBlocking<Unit> {
        val healthProfessional = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = null
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade deve ter o campo Tier Teórico definido")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST theoristTier is OPT_IN`() = runBlocking<Unit> {
        val healthProfessional = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.OPT_IN
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Tier Teórico não pode ser Opt-in")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#validate return error when active COMMUNITY_SPECIALIST has tier greater than theoristTier`() = runBlocking<Unit> {
        val healthProfessional = buildHealthProfessional(
            type = StaffType.COMMUNITY_SPECIALIST,
            healthSpecialistScore = HealthSpecialistScoreEnum.NEED_TO_RAISE_THE_BAR,
            theoristTier = SpecialistTier.EXPERT,
            tier = SpecialistTier.SUPER_EXPERT
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage("Tier de Atendimento não pode ser maior que o Tier Teórico")
            .ofType(InvalidArgumentException::class)
    }

    @Test
    fun `#isOnVacation return true if health professional is on vacation`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.minusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.isOnVacation()
                assertThat(result).isTrue()

            }
        }

    @Test
    fun `#isOnVacation return false if health professional is on vacation`() =
        mockLocalDateTime { date ->
            runBlocking {
                val healthProfessional = buildHealthProfessional(
                    type = StaffType.HEALTH_PROFESSIONAL,
                    onVacationStart = date.plusDays(5),
                    onVacationUntil = date.plusDays(10)
                )

                val result = healthProfessional.isOnVacation()
                assertThat(result).isFalse()

            }
        }

    @Test
    fun `#isOnVacation return false if health professional don't have any vacation date`() = runBlocking<Unit> {
            val healthProfessional = buildHealthProfessional(
                type = StaffType.HEALTH_PROFESSIONAL,
            )

            val result = healthProfessional.isOnVacation()
            assertThat(result).isFalse()

        }

    @ParameterizedTest
    @MethodSource("provideInvalidCouncil")
    fun `#validate return error for invalid council`(value: TestCase) = runBlocking {
        val healthProfessional = buildHealthProfessional(
            council = value.council
        )

        val result = healthProfessional.validate()
        ResultAssert(result)
            .fails()
            .withMessage(value.expectedMessage)
            .ofType(InvalidArgumentException::class)
    }

    private fun buildHealthProfessional(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        profileBio: String? = "bio",
        council: CouncilModel = CouncilModel(
            "1234",
            State.SP,
            CouncilType.CRM
        ),
        specialtyId: UUID? = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = listOf(RangeUUID.generate()),
        internalSpecialtyId: UUID? = null,
        internalSubSpecialtyIds: List<UUID> = emptyList(),
        quote: String? = "quote",
        urlSlug: String? = "url-slug",
        imageUrl: String? = null,
        staff: StaffModel? = null,
        onCall: Boolean = false,
        providerUnitIds: List<UUID> = emptyList(),
        email: String = "<EMAIL>",
        name: String = "João",
        gender: Gender? = null,
        nationalId: String? = null,
        type: StaffType = StaffType.PITAYA,
        role: Role? = null,
        education: List<String> = emptyList(),
        qualifications: List<Qualification> = emptyList(),
        appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
        addressesStructured: List<StructuredAddress>? = null,
        contactIds: List<UUID> = emptyList(),
        healthSpecialistScore: HealthSpecialistScoreEnum? = null,
        theoristTier: SpecialistTier? = null,
        tier: SpecialistTier? = null,
        onVacationUntil: LocalDateTime? = null,
        onVacationStart: LocalDateTime? = null
    ) = HealthProfessionalModel(
        id = id,
        staffId = staffId,
        profileBio = profileBio,
        council = council,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        internalSpecialtyId = internalSpecialtyId,
        internalSubSpecialtyIds = internalSubSpecialtyIds,
        quote = quote,
        education = education,
        qualifications = qualifications,
        urlSlug = urlSlug,
        imageUrl = imageUrl,
        staff = staff,
        onCall = onCall,
        providerUnitIds = providerUnitIds,
        email = email,
        name = name,
        gender = gender,
        nationalId = nationalId,
        type = type,
        role = role,
        appointmentTypes = appointmentTypes,
        addressesStructured = addressesStructured,
        contactIds = contactIds,
        healthSpecialistScore = healthSpecialistScore,
        theoristTier = theoristTier,
        tier = tier,
        onVacationUntil = onVacationUntil,
        onVacationStart = onVacationStart
    )

}
