package br.com.alice.staff.services

import br.com.alice.common.asyncLayer
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.currentUserId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.ContactModel
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.withContacts
import br.com.alice.data.layer.models.withStaff
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.data.layer.services.StaffModelDataService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.client.StaffFilters
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.event.StaffCreatedEvent
import br.com.alice.staff.event.StaffUpdatedEvent
import br.com.alice.staff.logics.StaffPredicatesLogic
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class StaffServiceImpl(
    private val staffDataService: StaffModelDataService,
    private val kafkaProducerService: KafkaProducerService,
    private val healthProfessionalDataService: HealthProfessionalModelDataService,
    private val contactService: ContactService,
) : StaffService {

    override suspend fun findWithAnyRole(
        roles: List<Role>,
    ): Result<List<Staff>, Throwable> =
        staffDataService.find { where { this.role.inList(roles) } }.mapEach { it.toTransport() }

    override suspend fun findActivesWithAnyRole(
        roles: List<Role>,
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where {
                this.role.inList(roles) and this.active.eq(true)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findByEmail(email: String): Result<Staff, Throwable> =
        staffDataService.findOne { where { this.email.eq(email) } }.map { it.toTransport() }

    override suspend fun findActiveByEmail(email: String): Result<Staff, Throwable> =
        staffDataService.findOne {
            where { this.email.eq(email) and this.active.eq(true) }
        }.map { it.toTransport() }

    override suspend fun findByEmailList(emails: List<String>): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where {
                this.email.inList(emails) and this.active.eq(true)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findByTokenAndRange(term: String, range: IntRange): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where { searchTokens.search(term) and this.active.eq(true) }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun findByRange(
        range: IntRange,
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            orderBy { email }
                .sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun get(
        id: UUID,
    ): Result<Staff, Throwable> = staffDataService.get(id).map { it.toTransport() }

    override suspend fun getActive(id: UUID): Result<Staff, Throwable> =
        staffDataService.findOne {
            where {
                this.id.eq(id) and this.active.eq(true)
            }
        }.map { it.toTransport() }

    override suspend fun saveProfileImageUrl(id: UUID, imageUrl: String): Result<Staff, Throwable> =
        staffDataService.get(id).flatMap { staff ->
            staffDataService.update(staff.copy(profileImageUrl = imageUrl))
        }.map { it.toTransport() }

    override suspend fun update(
        model: Staff,
        healthProfessional: HealthProfessional?,
        contacts: List<Contact>?
    ): Result<Staff, Throwable> = coroutineScope {
        span("staff update") { span ->
            val toUpdate = model.toModel()
            span.setAttribute("new_role", toUpdate.role.toString())
            span.setAttribute("staff_id", toUpdate.id.toString())
            span.setAttribute("is_health_professional", toUpdate.isHealthProfessionalOrNavigator())
            currentUserId()?.let { span.setAttribute("requester_id", it) }

            toUpdate.validate().thenError {
                return@span it.failure()
            }

            val healthProfessionalModel = if (toUpdate.active && healthProfessional != null) {
                healthProfessional.toModel().also { hp ->
                    hp.validate().thenError {
                        return@span it.failure()
                    }
                }
            } else null

            val currentStaff = staffDataService.get(toUpdate.id).then { currentStaff ->
                span.setAttribute("old_role", currentStaff.role.toString())
                span.setAttribute("role_has_changed", currentStaff.role != toUpdate.role)
            }.get()
            val staffUpdatedDeferred = async {
                staffDataService.update(toUpdate).then { updatedStaff ->
                    if (healthProfessionalModel != null) {
                        syncHealthProfessionalWithContact(updatedStaff, healthProfessionalModel, contacts?.map { it.toModel() })
                    }
                    kafkaProducerService.produce(
                        StaffUpdatedEvent(
                            toUpdate.id,
                            updatedStaff = updatedStaff.toTransport(),
                            oldStaff = currentStaff.toTransport()
                        )
                    )
                }
            }
            staffUpdatedDeferred.await().map { it.toTransport() }
        }
    }

    override suspend fun add(
        model: Staff,
        healthProfessional: HealthProfessional?,
        contacts: List<Contact>?
    ): Result<Staff, Throwable> {
        val toAdd = model.toModel()
        toAdd.validate().thenError {
            return it.failure()
        }

        val healthProfessionalModel = if (toAdd.active && healthProfessional != null) {
            healthProfessional.toModel().also { hp ->
                hp.validate().thenError {
                    return it.failure()
                }
            }
        } else null

        return staffDataService.add(toAdd)
            .map { staff ->
                if (toAdd.isHealthProfessionalOrNavigator() && healthProfessionalModel != null) {
                    addHealthProfessionalWithContact(staff, healthProfessionalModel, contacts?.map { it.toModel() }).get()
                }
                staff
            }.map { it.toTransport() }
            .then { kafkaProducerService.produce(StaffCreatedEvent(it)) }

    }

    @OptIn(QueryAllUsage::class)
    override suspend fun count(): Result<Int, Throwable> = staffDataService.count { all() }

    override suspend fun findByList(
        staffIds: List<UUID>,
    ): Result<List<Staff>, Throwable> =
        if (staffIds.isEmpty()) emptyList<Staff>().success()
        else staffDataService.find { where { id.inList(staffIds) } }.mapEach { it.toTransport() }

    override suspend fun searchActiveStaff(
        namePrefix: String,
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where {
                searchTokens.search(namePrefix) and this.active.eq(true)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findByNameWithRoleAndRange(
        name: String,
        roles: List<Role>,
        range: IntRange
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where {
                this.role.inList(roles) and
                        this.searchTokens.search(name)
            }.orderBy { email }
                .sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun findByRoleAndRange(
        role: Role,
        range: IntRange
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where { this.role.eq(role) }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun searchByNameAndRoleWithRange(
        ids: List<UUID>?,
        range: IntRange,
        roles: List<Role>?,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>?
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            StaffPredicatesLogic.buildFilterQuery(
                range, ids, namePrefix, roles, active, types
            )
        }.mapEach { it.toTransport() }

    override suspend fun searchActivesByIdsNameAndRole(
        ids: List<UUID>?,
        name: String?,
        role: Role
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            StaffPredicatesLogic.buildFilterQuery(
                ids, name, listOf(role), true
            )
        }.mapEach { it.toTransport() }

    override suspend fun countByNameAndRoleWithRange(
        ids: List<UUID>?,
        roles: List<Role>?,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>?
    ): Result<Int, Throwable> =
        staffDataService.count {
            StaffPredicatesLogic.buildFilterQuery(ids, namePrefix, roles, active, types)
        }

    override suspend fun countByRole(
        roles: List<Role>
    ): Result<Int, Throwable> = staffDataService.count {
        where { this.role.inList(roles) }
    }

    override suspend fun getActiveWithRole(id: UUID, roles: List<Role>): Result<Staff, Throwable> =
        staffDataService.findOne {
            where {
                this.id.eq(id) and
                        this.active.eq(true) and
                        this.role.inList(roles)
            }
        }.map { it.toTransport() }

    override suspend fun findActivesById(
        ids: List<UUID>,
    ): Result<List<Staff>, Throwable> =
        staffDataService.find {
            where {
                id.inList(ids) and this.active.eq(true)
            }
        }.mapEach { it.toTransport() }

    override suspend fun findBy(filters: StaffFilters): Result<List<Staff>, Throwable> =
        if (!filters.isValidate()) emptyList<Staff>().success()
        else staffDataService.find {
            where {
                filters.toPredicate(this)!!
            }.let { query ->
                filters.range?.let { range ->
                    query
                        .offset { range.first }
                        .limit { range.count() }
                } ?: query
            }
        }.mapEach { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun countBy(filters: StaffFilters): Result<Int, Throwable> =
        staffDataService.count {
            filters.toPredicate(this.fieldOptions)?.let { where { it } } ?: all()
        }

    override suspend fun countActiveByToken(term: String, active: Boolean): Result<Int, Throwable> =
        useReadDatabase {
            staffDataService.count {
                where { searchTokens.search(term) and this.active.eq(active) }
            }
        }

    override suspend fun setOnCall(staffId: UUID, onCall: Boolean): Result<Staff, Throwable> =
        staffDataService.get(staffId).flatMap { staff ->
            staffDataService.update(staff.copy(onCall = onCall))
        }.then { staff ->
            healthProfessionalDataService.findOne {
                where { this.staffId.eq(staffId) }
            }.map {
                healthProfessionalDataService.update(it.copy(onCall = onCall))
                staff
            }.coFoldNotFound { staff.success() }
        }.map { it.toTransport() }

    private suspend fun syncHealthProfessionalWithContact(
        staff: StaffModel,
        healthProfessional: HealthProfessionalModel,
        contacts: List<ContactModel>? = null
    ) = healthProfessionalDataService.findOne {
        where { this.staffId.eq(staff.id) }
    }.map { currentHp ->
        updateHealthProfessionalWithContact(currentHp, healthProfessional, contacts)
    }.coFoldNotFound {
        addHealthProfessionalWithContact(staff, healthProfessional, contacts)
    }

    private suspend fun upsertContact(
        oldModel: HealthProfessionalModel?,
        newModel: HealthProfessionalModel,
        contacts: List<ContactModel>?
    ): Result<HealthProfessionalModel, Throwable> = coroutineScope {
        val contactUpsert = async {
            contacts?.pmap { contact ->
                contactService.upsertWithAddress(contact.toTransport(), contact.address).get()
            }
        }
        val contactsToDelete =
            oldModel?.contactIds?.filter { contactId -> contacts?.none { it.id == contactId } ?: true } ?: emptyList()
        contactsToDelete.takeIf { it.isNotEmpty() }?.pmap { contactId ->
            contactService.get(contactId).getOrNullIfNotFound()?.let { contactService.inactiveWithAddress(it) }?.get()
        }

        newModel.copy(contacts = contactUpsert.await()?.map { it.toModel() }).success()
    }

    private suspend fun addHealthProfessionalWithContact(
        staff: StaffModel,
        healthProfessional: HealthProfessionalModel,
        contacts: List<ContactModel>?
    ) = healthProfessionalDataService.add(
        buildUpdateHealthProfessionalWithContact(null, healthProfessional, contacts)
    ).flatMap {
        upsertContact(null, healthProfessional, contacts)
    }.then {
        val toPublish = it.withStaff(staff).withContacts(contacts).toTransport()
        kafkaProducerService.produce(HealthProfessionalCreatedEvent(toPublish))
        kafkaProducerService.produce(HealthProfessionalChangedEvent(toPublish, NotificationEventAction.CREATED))
    }

    private suspend fun updateHealthProfessionalWithContact(
        currentHp: HealthProfessionalModel,
        newHp: HealthProfessionalModel,
        contacts: List<ContactModel>?
    ) = healthProfessionalDataService.update(
        buildUpdateHealthProfessionalWithContact(currentHp, newHp, contacts)
    ).flatMap {
        upsertContact(currentHp, it, contacts)
    }.then {
        kafkaProducerService.produce(HealthProfessionalUpdatedEvent(it.id))
        kafkaProducerService.produce(HealthProfessionalChangedEvent(it.toTransport(), NotificationEventAction.UPDATED))
    }

    private fun buildUpdateHealthProfessionalWithContact(
        currentHp: HealthProfessionalModel?,
        newHp: HealthProfessionalModel,
        contacts: List<ContactModel>?
    ) = (currentHp?.copy(
        email = newHp.email,
        name = newHp.name,
        status = newHp.status,
        nationalId = newHp.nationalId,
        type = newHp.type,
        gender = newHp.gender,
        role = newHp.role,
        profileBio = newHp.profileBio,
        council = newHp.council,
        specialtyId = newHp.specialtyId,
        subSpecialtyIds = newHp.subSpecialtyIds,
        internalSpecialtyId = newHp.internalSpecialtyId,
        internalSubSpecialtyIds = newHp.internalSubSpecialtyIds,
        quote = newHp.quote,
        qualifications = newHp.qualifications,
        imageUrl = newHp.imageUrl,
        education = newHp.education,
        theoristTier = newHp.theoristTier,
        tier = newHp.tier,
        providerUnitIds = newHp.providerUnitIds,
        showOnApp = newHp.showOnApp,
        urlSlug = newHp.urlSlug,
        healthSpecialistScore = newHp.healthSpecialistScore,
        paymentFrequency = newHp.paymentFrequency,
        curiosity = newHp.curiosity,
        deAccreditationDate = newHp.deAccreditationDate,
        onVacationStart = newHp.onVacationStart,
        onVacationUntil = newHp.onVacationUntil,
    ) ?: newHp).copy(
        scheduleAvailabilityDays = contacts?.mapNotNull { it.scheduleAvailabilityDays }?.firstNotNullOfOrNull { it },
        appointmentTypes = contacts?.map { it.modality }?.distinct()?.map { it.toAppointment() }.orEmpty(),
        contactIds = contacts?.map { it.id }.orEmpty(),
        phones = contacts?.map { it.phones }?.flatten()?.distinctBy { it.phone }.orEmpty(),
    )

    @OptIn(WithFilterPredicateUsage::class)
    private fun StaffFilters.toPredicate(fieldOptions: StaffModelDataService.FieldOptions) =
        basePredicateForFilters()
            .withFilter(roles) { fieldOptions.role.inList(it) }
            .withFilter(emails) { fieldOptions.email.inList(it) }
            .withFilter(searchTerm) { fieldOptions.searchTokens.search(it) }
            .withFilter(ids) { fieldOptions.id.inList(it) }
            .withFilter(namePrefix) { fieldOptions.firstName.like(it) }
            .withFilter(active) { fieldOptions.active.eq(it) }
            .withFilter(types) { fieldOptions.type.inList(it) }

    private fun ModalityType.toAppointment() = when (this) {
        ModalityType.REMOTE -> SpecialistAppointmentType.REMOTE
        ModalityType.PRESENTIAL -> SpecialistAppointmentType.PRESENTIAL
    }

}
